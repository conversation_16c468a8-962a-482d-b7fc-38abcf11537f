# WordSpy - Multiplayer Word Guessing Game

WordSpy is a fully browser-based multiplayer game built with Next.js 15+, Socket.IO, and MongoDB Atlas. Players must find the imposter among them by analyzing word descriptions.

## Game Concept

- Players join lobbies and choose usernames
- One random player becomes the imposter and receives a different word
- Players take turns describing their word with one word
- After all players have described, everyone votes to eliminate the imposter
- The game continues until the imposter is found or only 3 players remain

## Features

- **Real-time multiplayer**: Socket.IO for instant communication
- **Lobby system**: Create and join game lobbies with optional passwords
- **Player customization**: Choose username colors
- **Turn-based gameplay**: 30-second turns with automatic progression
- **Voting system**: Democratic elimination process
- **Clean UI**: Skribbl.io-inspired minimal design
- **Responsive design**: Works on desktop and mobile

## Tech Stack

- **Frontend**: Next.js 15+ (App Router), React 19, TypeScript
- **Styling**: Tailwind CSS
- **Real-time**: Socket.IO
- **Database**: MongoDB Atlas
- **Deployment**: Vercel-ready

## Setup Instructions

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd WordSpy
npm install
```

### 2. Database Setup

1. Create a MongoDB Atlas account at [mongodb.com](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get your connection string
4. Update `.env.local` with your MongoDB URI:

```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/wordspy?retryWrites=true&w=majority
```

### 3. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to play the game.

## How to Play

1. **Choose Username**: Enter a username when you first visit
2. **Create/Join Lobby**: Create a new lobby or join an existing one
3. **Wait for Players**: Need at least 3 players to start
4. **Game Starts**: One player becomes the imposter with a different word
5. **Take Turns**: Each player describes their word with ONE word (30 seconds)
6. **Vote**: After all descriptions, vote for who you think is the imposter
7. **Win Conditions**:
   - **Civilians win**: If they eliminate the imposter
   - **Imposter wins**: If they survive until only 3 players remain

## Game Rules

- Minimum 3 players, maximum 8 players per lobby
- Each player gets 30 seconds for their turn
- Only one word allowed per description
- No time limit for voting
- Game continues until imposter is eliminated or 3 players remain

## Deployment

### Vercel Deployment

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add your MongoDB URI to Vercel environment variables
4. Deploy!

The app is designed to work seamlessly on Vercel with no additional configuration needed.

## Project Structure

```
WordSpy/
├── app/                    # Next.js App Router pages
│   ├── page.tsx           # Homepage with lobby list
│   ├── lobby/[id]/        # Lobby waiting room
│   └── game/[id]/         # Game interface
├── components/            # React components
├── lib/                   # Utilities (MongoDB connection)
├── pages/api/             # Socket.IO server
├── types/                 # TypeScript definitions
└── public/               # Static assets
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - feel free to use this project for learning or building your own games!

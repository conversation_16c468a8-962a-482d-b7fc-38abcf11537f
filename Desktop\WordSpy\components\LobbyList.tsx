'use client';

import { Lobby } from '@/types/game';

interface LobbyListProps {
  lobbies: Lobby[];
  onJoinLobby: (lobbyId: string, password?: string) => void;
}

export default function LobbyList({ lobbies, onJoinLobby }: LobbyListProps) {
  const handleJoinClick = (lobby: Lobby) => {
    if (lobby.password) {
      const password = prompt('Enter lobby password:');
      if (password !== null) {
        onJoinLobby(lobby.id, password);
      }
    } else {
      onJoinLobby(lobby.id);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold mb-4">Active Lobbies</h2>
      {lobbies.length === 0 ? (
        <div className="text-gray-500 text-center py-8">
          No active lobbies. Create one to get started!
        </div>
      ) : (
        <div className="space-y-3">
          {lobbies.map((lobby) => (
            <div
              key={lobby.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold">{lobby.name}</h3>
                  {lobby.password && (
                    <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                      🔒 Private
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {lobby.players.filter(p => p.isConnected).length}/{lobby.maxPlayers} players
                  {lobby.gameState.phase !== 'waiting' && (
                    <span className="ml-2 text-green-600">• In Game</span>
                  )}
                </div>
              </div>
              <button
                onClick={() => handleJoinClick(lobby)}
                disabled={lobby.players.filter(p => p.isConnected).length >= lobby.maxPlayers}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {lobby.players.filter(p => p.isConnected).length >= lobby.maxPlayers ? 'Full' : 'Join'}
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export interface Player {
  id: string;
  username: string;
  color?: string;
  isHost: boolean;
  isImposter?: boolean;
  word?: string;
  hasSubmittedWord?: boolean;
  votedFor?: string;
  isConnected: boolean;
}

export interface Lobby {
  _id?: string;
  id: string;
  name: string;
  password?: string;
  hostId: string;
  players: Player[];
  maxPlayers: number;
  isActive: boolean;
  gameState: GameState;
  createdAt: Date;
  updatedAt: Date;
}

export interface GameState {
  phase: 'waiting' | 'playing' | 'voting' | 'ended';
  currentTurn: number;
  currentPlayerIndex: number;
  round: number;
  words: {
    normal: string;
    imposter: string;
  };
  submissions: { [playerId: string]: string };
  votes: { [playerId: string]: string };
  timeLeft: number;
  eliminatedPlayers: string[];
}

export interface WordPair {
  normal: string;
  imposter: string;
}

export const WORD_PAIRS: WordPair[] = [
  { normal: "Donald Duck", imposter: "Mickey Mouse" },
  { normal: "Pizza", imposter: "Burger" },
  { normal: "<PERSON>", imposter: "<PERSON>" },
  { normal: "<PERSON>", imposter: "<PERSON>" },
  { normal: "Summer", imposter: "Winter" },
  { normal: "Ocean", imposter: "Lake" },
  { normal: "Guitar", imposter: "Piano" },
  { normal: "Apple", imposter: "Orange" },
  { normal: "Book", imposter: "Magazine" },
  { normal: "Car", imposter: "Bicycle" },
  { normal: "Mountain", imposter: "Hill" },
  { normal: "Doctor", imposter: "Nurse" },
  { normal: "Football", imposter: "Basketball" },
  { normal: "Airplane", imposter: "Helicopter" },
  { normal: "Chocolate", imposter: "Vanilla" },
  { normal: "Lion", imposter: "Tiger" },
  { normal: "Beach", imposter: "Desert" },
  { normal: "Painting", imposter: "Drawing" },
  { normal: "Restaurant", imposter: "Cafe" },
  { normal: "Smartphone", imposter: "Tablet" }
];

export const PLAYER_COLORS = [
  '#FF6B6B', // Red
  '#4ECDC4', // Teal
  '#45B7D1', // Blue
  '#96CEB4', // Green
  '#FFEAA7', // Yellow
  '#DDA0DD', // Plum
  '#98D8C8', // Mint
  '#F7DC6F', // Light Yellow
  '#BB8FCE', // Light Purple
  '#85C1E9', // Light Blue
  '#F8C471', // Orange
  '#82E0AA'  // Light Green
];

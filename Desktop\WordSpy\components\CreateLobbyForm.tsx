'use client';

import { useState } from 'react';

interface CreateLobbyFormProps {
  onCreateLobby: (name: string, password?: string) => void;
}

export default function CreateLobbyForm({ onCreateLobby }: CreateLobbyFormProps) {
  const [lobbyName, setLobbyName] = useState('');
  const [password, setPassword] = useState('');
  const [usePassword, setUsePassword] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (lobbyName.trim()) {
      onCreateLobby(lobbyName.trim(), usePassword ? password : undefined);
      setLobbyName('');
      setPassword('');
      setUsePassword(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-bold mb-4">Create New Lobby</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="lobbyName" className="block text-sm font-medium text-gray-700 mb-2">
            Lobby Name
          </label>
          <input
            type="text"
            id="lobbyName"
            value={lobbyName}
            onChange={(e) => setLobbyName(e.target.value)}
            placeholder="Enter lobby name..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxLength={30}
            required
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="usePassword"
            checked={usePassword}
            onChange={(e) => setUsePassword(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="usePassword" className="text-sm text-gray-700">
            Private lobby (password protected)
          </label>
        </div>

        {usePassword && (
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              maxLength={20}
              required={usePassword}
            />
          </div>
        )}

        <button
          type="submit"
          disabled={!lobbyName.trim() || (usePassword && !password.trim())}
          className="w-full px-4 py-2 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Create Lobby
        </button>
      </form>
    </div>
  );
}

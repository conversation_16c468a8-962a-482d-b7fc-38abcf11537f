import { Server } from 'socket.io';
import { getDatabase } from '../../lib/mongodb';
import { v4 as uuidv4 } from 'uuid';

const WORD_PAIRS = [
  { normal: "Donald Duck", imposter: "Mickey Mouse" },
  { normal: "Pizza", imposter: "Burger" },
  { normal: "Cat", imposter: "Dog" },
  { normal: "Coffee", imposter: "Tea" },
  { normal: "Summer", imposter: "Winter" },
  { normal: "Ocean", imposter: "Lake" },
  { normal: "Guitar", imposter: "Piano" },
  { normal: "Apple", imposter: "Orange" },
  { normal: "Book", imposter: "Magazine" },
  { normal: "Car", imposter: "Bicycle" },
  { normal: "Mountain", imposter: "Hill" },
  { normal: "Doctor", imposter: "Nurse" },
  { normal: "Football", imposter: "Basketball" },
  { normal: "Airplane", imposter: "Helicopter" },
  { normal: "Chocolate", imposter: "Vanilla" },
  { normal: "Lion", imposter: "<PERSON>" },
  { normal: "Beach", imposter: "Desert" },
  { normal: "Painting", imposter: "Drawing" },
  { normal: "Restaurant", imposter: "Cafe" },
  { normal: "Smartphone", imposter: "Tablet" }
];

// Timer management
const activeTimers = new Map();

async function startTurnTimer(lobbyId, io, db) {
  // Clear existing timer
  if (activeTimers.has(lobbyId)) {
    clearInterval(activeTimers.get(lobbyId));
  }

  let timeLeft = 30;
  const timer = setInterval(async () => {
    timeLeft--;

    try {
      await db.collection('lobbies').updateOne(
        { id: lobbyId },
        { $set: { 'gameState.timeLeft': timeLeft } }
      );

      const lobby = await db.collection('lobbies').findOne({ id: lobbyId });
      if (lobby) {
        io.to(lobbyId).emit('timer-update', { timeLeft });
      }

      if (timeLeft <= 0) {
        clearInterval(timer);
        activeTimers.delete(lobbyId);

        // Move to next turn or voting
        await handleTurnEnd(lobbyId, io, db);
      }
    } catch (error) {
      console.error('Timer error:', error);
      clearInterval(timer);
      activeTimers.delete(lobbyId);
    }
  }, 1000);

  activeTimers.set(lobbyId, timer);
}

async function handleTurnEnd(lobbyId, io, db) {
  try {
    const lobby = await db.collection('lobbies').findOne({ id: lobbyId });
    if (!lobby || lobby.gameState.phase !== 'playing') return;

    const activePlayers = lobby.players.filter(p => p.isConnected && !lobby.gameState.eliminatedPlayers.includes(p.id));
    const nextPlayerIndex = (lobby.gameState.currentPlayerIndex + 1) % activePlayers.length;
    const nextTurn = lobby.gameState.currentTurn + 1;

    if (nextTurn > activePlayers.length) {
      // All players have had their turn, move to voting
      await db.collection('lobbies').updateOne(
        { id: lobbyId },
        {
          $set: {
            'gameState.phase': 'voting',
            'gameState.timeLeft': 0,
            updatedAt: new Date()
          }
        }
      );

      const votingLobby = await db.collection('lobbies').findOne({ id: lobbyId });
      io.to(lobbyId).emit('voting-started', votingLobby);
    } else {
      // Move to next player's turn
      await db.collection('lobbies').updateOne(
        { id: lobbyId },
        {
          $set: {
            'gameState.currentTurn': nextTurn,
            'gameState.currentPlayerIndex': nextPlayerIndex,
            'gameState.timeLeft': 30,
            updatedAt: new Date()
          }
        }
      );

      const updatedLobby = await db.collection('lobbies').findOne({ id: lobbyId });
      io.to(lobbyId).emit('game-updated', updatedLobby);

      // Start timer for next turn
      startTurnTimer(lobbyId, io, db);
    }
  } catch (error) {
    console.error('Error handling turn end:', error);
  }
}

export default function handler(_req, res) {
  if (res.socket.server.io) {
    console.log('Socket.IO already running');
  } else {
    console.log('Socket.IO starting');
    const io = new Server(res.socket.server);
    res.socket.server.io = io;

    io.on('connection', (socket) => {
      console.log('User connected:', socket.id);

      // Get active lobbies
      socket.on('get-lobbies', async () => {
        try {
          const db = await getDatabase();
          const lobbies = await db.collection('lobbies')
            .find({ isActive: true })
            .sort({ createdAt: -1 })
            .toArray();
          socket.emit('lobbies-list', lobbies);
        } catch (error) {
          console.error('Error getting lobbies:', error);
          socket.emit('error', 'Failed to get lobbies');
        }
      });

      // Create lobby
      socket.on('create-lobby', async (data) => {
        try {
          const db = await getDatabase();
          const lobbyId = uuidv4();
          
          const lobby = {
            id: lobbyId,
            name: data.name,
            password: data.password,
            hostId: socket.id,
            players: [{ ...data.host, id: socket.id, isHost: true, isConnected: true }],
            maxPlayers: 8,
            isActive: true,
            gameState: {
              phase: 'waiting',
              currentTurn: 0,
              currentPlayerIndex: 0,
              round: 1,
              words: { normal: '', imposter: '' },
              submissions: {},
              votes: {},
              timeLeft: 0,
              eliminatedPlayers: []
            },
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await db.collection('lobbies').insertOne(lobby);
          socket.join(lobbyId);
          socket.emit('lobby-created', lobby);
          
          // Broadcast updated lobby list
          const lobbies = await db.collection('lobbies')
            .find({ isActive: true })
            .sort({ createdAt: -1 })
            .toArray();
          io.emit('lobbies-list', lobbies);
          
        } catch (error) {
          console.error('Error creating lobby:', error);
          socket.emit('error', 'Failed to create lobby');
        }
      });

      // Join lobby
      socket.on('join-lobby', async (data) => {
        try {
          const db = await getDatabase();
          const lobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
          
          if (!lobby) {
            socket.emit('error', 'Lobby not found');
            return;
          }

          if (lobby.password && lobby.password !== data.password) {
            socket.emit('error', 'Incorrect password');
            return;
          }

          if (lobby.players.length >= lobby.maxPlayers) {
            socket.emit('error', 'Lobby is full');
            return;
          }

          // Check if player already exists (reconnection)
          const existingPlayerIndex = lobby.players.findIndex(p => p.username === data.player.username);
          
          if (existingPlayerIndex >= 0) {
            // Update existing player
            await db.collection('lobbies').updateOne(
              { id: data.lobbyId, 'players.username': data.player.username },
              { 
                $set: { 
                  'players.$.id': socket.id,
                  'players.$.isConnected': true,
                  updatedAt: new Date()
                }
              }
            );
          } else {
            // Add new player
            const updatedPlayer = { ...data.player, id: socket.id, isConnected: true };
            await db.collection('lobbies').updateOne(
              { id: data.lobbyId },
              { 
                $push: { players: updatedPlayer },
                $set: { updatedAt: new Date() }
              }
            );
          }

          socket.join(data.lobbyId);
          
          // Get updated lobby
          const updatedLobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
          io.to(data.lobbyId).emit('lobby-updated', updatedLobby);
          
          // Broadcast updated lobby list
          const lobbies = await db.collection('lobbies')
            .find({ isActive: true })
            .sort({ createdAt: -1 })
            .toArray();
          io.emit('lobbies-list', lobbies);
          
        } catch (error) {
          console.error('Error joining lobby:', error);
          socket.emit('error', 'Failed to join lobby');
        }
      });

      // Update player color
      socket.on('update-color', async (data) => {
        try {
          const db = await getDatabase();
          await db.collection('lobbies').updateOne(
            { id: data.lobbyId, 'players.id': socket.id },
            { 
              $set: { 
                'players.$.color': data.color,
                updatedAt: new Date()
              }
            }
          );

          const updatedLobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
          io.to(data.lobbyId).emit('lobby-updated', updatedLobby);
          
        } catch (error) {
          console.error('Error updating color:', error);
          socket.emit('error', 'Failed to update color');
        }
      });

      // Start game
      socket.on('start-game', async (data) => {
        try {
          const db = await getDatabase();
          const lobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
          
          if (!lobby || lobby.hostId !== socket.id) {
            socket.emit('error', 'Not authorized to start game');
            return;
          }

          if (lobby.players.length < 3) {
            socket.emit('error', 'Need at least 3 players to start');
            return;
          }

          // Select random word pair
          const wordPair = WORD_PAIRS[Math.floor(Math.random() * WORD_PAIRS.length)];
          
          // Select random imposter
          const imposterIndex = Math.floor(Math.random() * lobby.players.length);
          
          // Update players with words
          const updatedPlayers = lobby.players.map((player, index) => ({
            ...player,
            isImposter: index === imposterIndex,
            word: index === imposterIndex ? wordPair.imposter : wordPair.normal,
            hasSubmittedWord: false,
            votedFor: undefined
          }));

          const gameState = {
            phase: 'playing',
            currentTurn: 1,
            currentPlayerIndex: 0,
            round: 1,
            words: wordPair,
            submissions: {},
            votes: {},
            timeLeft: 30,
            eliminatedPlayers: []
          };

          await db.collection('lobbies').updateOne(
            { id: data.lobbyId },
            { 
              $set: { 
                players: updatedPlayers,
                gameState,
                updatedAt: new Date()
              }
            }
          );

          const updatedLobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
          io.to(data.lobbyId).emit('game-started', updatedLobby);

          // Start turn timer
          startTurnTimer(data.lobbyId, io, db);
          
        } catch (error) {
          console.error('Error starting game:', error);
          socket.emit('error', 'Failed to start game');
        }
      });

      // Submit word during turn
      socket.on('submit-word', async (data) => {
        try {
          const db = await getDatabase();
          const lobby = await db.collection('lobbies').findOne({ id: data.lobbyId });

          if (!lobby || lobby.gameState.phase !== 'playing') {
            socket.emit('error', 'Game not in playing phase');
            return;
          }

          const player = lobby.players.find(p => p.id === socket.id);
          if (!player) {
            socket.emit('error', 'Player not found');
            return;
          }

          // Update submissions
          const updatedSubmissions = { ...lobby.gameState.submissions };
          updatedSubmissions[socket.id] = data.word;

          // Mark player as having submitted
          const updatedPlayers = lobby.players.map(p =>
            p.id === socket.id ? { ...p, hasSubmittedWord: true } : p
          );

          await db.collection('lobbies').updateOne(
            { id: data.lobbyId },
            {
              $set: {
                'gameState.submissions': updatedSubmissions,
                players: updatedPlayers,
                updatedAt: new Date()
              }
            }
          );

          const updatedLobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
          io.to(data.lobbyId).emit('game-updated', updatedLobby);

          // Check if all players have submitted for this round
          const activePlayers = lobby.players.filter(p => p.isConnected && !lobby.gameState.eliminatedPlayers.includes(p.id));
          const allSubmitted = activePlayers.every(p => updatedSubmissions[p.id]);

          if (allSubmitted && lobby.gameState.currentTurn >= activePlayers.length) {
            // Move to voting phase
            await db.collection('lobbies').updateOne(
              { id: data.lobbyId },
              {
                $set: {
                  'gameState.phase': 'voting',
                  'gameState.timeLeft': 0,
                  updatedAt: new Date()
                }
              }
            );

            const votingLobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
            io.to(data.lobbyId).emit('voting-started', votingLobby);
          }

        } catch (error) {
          console.error('Error submitting word:', error);
          socket.emit('error', 'Failed to submit word');
        }
      });

      // Submit vote
      socket.on('submit-vote', async (data) => {
        try {
          const db = await getDatabase();
          const lobby = await db.collection('lobbies').findOne({ id: data.lobbyId });

          if (!lobby || lobby.gameState.phase !== 'voting') {
            socket.emit('error', 'Game not in voting phase');
            return;
          }

          const player = lobby.players.find(p => p.id === socket.id);
          if (!player || lobby.gameState.eliminatedPlayers.includes(socket.id)) {
            socket.emit('error', 'Cannot vote');
            return;
          }

          // Update votes
          const updatedVotes = { ...lobby.gameState.votes };
          updatedVotes[socket.id] = data.votedPlayerId;

          // Mark player as having voted
          const updatedPlayers = lobby.players.map(p =>
            p.id === socket.id ? { ...p, votedFor: data.votedPlayerId } : p
          );

          await db.collection('lobbies').updateOne(
            { id: data.lobbyId },
            {
              $set: {
                'gameState.votes': updatedVotes,
                players: updatedPlayers,
                updatedAt: new Date()
              }
            }
          );

          const updatedLobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
          io.to(data.lobbyId).emit('game-updated', updatedLobby);

          // Check if all players have voted
          const activePlayers = lobby.players.filter(p => p.isConnected && !lobby.gameState.eliminatedPlayers.includes(p.id));
          const allVoted = activePlayers.every(p => updatedVotes[p.id]);

          if (allVoted) {
            // Process votes and eliminate player
            const voteCount = {};
            Object.values(updatedVotes).forEach(votedId => {
              voteCount[votedId] = (voteCount[votedId] || 0) + 1;
            });

            // Find player with most votes
            let maxVotes = 0;
            let eliminatedPlayerId = null;
            Object.entries(voteCount).forEach(([playerId, votes]) => {
              if (votes > maxVotes) {
                maxVotes = votes;
                eliminatedPlayerId = playerId;
              }
            });

            if (eliminatedPlayerId) {
              const newEliminatedPlayers = [...lobby.gameState.eliminatedPlayers, eliminatedPlayerId];
              const remainingPlayers = activePlayers.filter(p => !newEliminatedPlayers.includes(p.id));

              // Check win conditions
              const imposter = lobby.players.find(p => p.isImposter);
              const imposterEliminated = newEliminatedPlayers.includes(imposter.id);

              if (imposterEliminated || remainingPlayers.length <= 3) {
                // Game ends
                await db.collection('lobbies').updateOne(
                  { id: data.lobbyId },
                  {
                    $set: {
                      'gameState.phase': 'ended',
                      'gameState.eliminatedPlayers': newEliminatedPlayers,
                      updatedAt: new Date()
                    }
                  }
                );
              } else {
                // Continue game - reset for next round
                const resetPlayers = lobby.players.map(p => ({
                  ...p,
                  hasSubmittedWord: false,
                  votedFor: undefined
                }));

                await db.collection('lobbies').updateOne(
                  { id: data.lobbyId },
                  {
                    $set: {
                      'gameState.phase': 'playing',
                      'gameState.currentTurn': 1,
                      'gameState.currentPlayerIndex': 0,
                      'gameState.round': lobby.gameState.round + 1,
                      'gameState.submissions': {},
                      'gameState.votes': {},
                      'gameState.timeLeft': 30,
                      'gameState.eliminatedPlayers': newEliminatedPlayers,
                      players: resetPlayers,
                      updatedAt: new Date()
                    }
                  }
                );
              }

              const finalLobby = await db.collection('lobbies').findOne({ id: data.lobbyId });
              io.to(data.lobbyId).emit('game-updated', finalLobby);
            }
          }

        } catch (error) {
          console.error('Error submitting vote:', error);
          socket.emit('error', 'Failed to submit vote');
        }
      });

      socket.on('disconnect', async () => {
        console.log('User disconnected:', socket.id);

        try {
          const db = await getDatabase();
          // Mark player as disconnected in all lobbies
          await db.collection('lobbies').updateMany(
            { 'players.id': socket.id },
            {
              $set: {
                'players.$.isConnected': false,
                updatedAt: new Date()
              }
            }
          );

          // Broadcast updated lobby list
          const lobbies = await db.collection('lobbies')
            .find({ isActive: true })
            .sort({ createdAt: -1 })
            .toArray();
          io.emit('lobbies-list', lobbies);

        } catch (error) {
          console.error('Error handling disconnect:', error);
        }
      });
    });
  }
  res.end();
}

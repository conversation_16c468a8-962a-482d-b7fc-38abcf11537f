'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { io, Socket } from 'socket.io-client';
import UsernameModal from '@/components/UsernameModal';
import LobbyList from '@/components/LobbyList';
import CreateLobbyForm from '@/components/CreateLobbyForm';
import { Lobby, Player, PLAYER_COLORS } from '@/types/game';

export default function Home() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [username, setUsername] = useState('');
  const [showUsernameModal, setShowUsernameModal] = useState(true);
  const [lobbies, setLobbies] = useState<Lobby[]>([]);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    // Check if username exists in localStorage
    const savedUsername = localStorage.getItem('wordspy-username');
    if (savedUsername) {
      setUsername(savedUsername);
      setShowUsernameModal(false);
    }
  }, []);

  useEffect(() => {
    if (username && !socket) {
      // Initialize socket connection
      const newSocket = io();
      setSocket(newSocket);

      newSocket.on('connect', () => {
        console.log('Connected to server');
        newSocket.emit('get-lobbies');
      });

      newSocket.on('lobbies-list', (lobbiesList: Lobby[]) => {
        setLobbies(lobbiesList);
      });

      newSocket.on('lobby-created', (lobby: Lobby) => {
        router.push(`/lobby/${lobby.id}`);
      });

      newSocket.on('error', (message: string) => {
        setError(message);
        setTimeout(() => setError(''), 5000);
      });

      return () => {
        newSocket.close();
      };
    }
  }, [username, socket, router]);

  const handleUsernameSubmit = (newUsername: string) => {
    setUsername(newUsername);
    setShowUsernameModal(false);
  };

  const handleCreateLobby = (name: string, password?: string) => {
    if (!socket) return;

    const player: Player = {
      id: '',
      username,
      color: PLAYER_COLORS[Math.floor(Math.random() * PLAYER_COLORS.length)],
      isHost: true,
      isConnected: true
    };

    socket.emit('create-lobby', { name, password, host: player });
  };

  const handleJoinLobby = (lobbyId: string, password?: string) => {
    if (!socket) return;

    const player: Player = {
      id: '',
      username,
      color: PLAYER_COLORS[Math.floor(Math.random() * PLAYER_COLORS.length)],
      isHost: false,
      isConnected: true
    };

    socket.emit('join-lobby', { lobbyId, password, player });
    router.push(`/lobby/${lobbyId}`);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <UsernameModal
        isOpen={showUsernameModal}
        onSubmit={handleUsernameSubmit}
      />

      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">WordSpy</h1>
          <p className="text-gray-600">Find the imposter among you!</p>
          {username && (
            <p className="text-sm text-gray-500 mt-2">
              Playing as: <span className="font-semibold">{username}</span>
            </p>
          )}
        </header>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <CreateLobbyForm onCreateLobby={handleCreateLobby} />
          <LobbyList lobbies={lobbies} onJoinLobby={handleJoinLobby} />
        </div>
      </div>
    </div>
  );
}

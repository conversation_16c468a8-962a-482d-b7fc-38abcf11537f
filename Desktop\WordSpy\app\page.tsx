'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import UsernameModal from '@/components/UsernameModal';
import LobbyList from '@/components/LobbyList';
import CreateLobbyForm from '@/components/CreateLobbyForm';
import { Lobby, Player, PLAYER_COLORS } from '@/types/game';
import { gameAPI } from '@/lib/api';

export default function Home() {
  const [username, setUsername] = useState('');
  const [showUsernameModal, setShowUsernameModal] = useState(true);
  const [lobbies, setLobbies] = useState<Lobby[]>([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if username exists in localStorage
    const savedUsername = localStorage.getItem('wordspy-username');
    if (savedUsername) {
      setUsername(savedUsername);
      setShowUsernameModal(false);
    }
  }, []);

  useEffect(() => {
    if (username) {
      loadLobbies();
      // Set up polling for lobby updates
      const interval = setInterval(loadLobbies, 3000);
      return () => clearInterval(interval);
    }
  }, [username]);

  const loadLobbies = async () => {
    try {
      const lobbiesList = await gameAPI.getLobbies();
      setLobbies(lobbiesList);
    } catch (error) {
      console.error('Error loading lobbies:', error);
      setError('Failed to load lobbies');
      setTimeout(() => setError(''), 5000);
    }
  };

  const handleUsernameSubmit = (newUsername: string) => {
    setUsername(newUsername);
    setShowUsernameModal(false);
  };

  const handleCreateLobby = async (name: string, password?: string) => {
    if (loading) return;

    setLoading(true);
    try {
      const player: Player = {
        id: gameAPI.getPlayerId(),
        username,
        color: PLAYER_COLORS[Math.floor(Math.random() * PLAYER_COLORS.length)],
        isHost: true,
        isConnected: true
      };

      const lobby = await gameAPI.createLobby(name, password || '', player);
      router.push(`/lobby/${lobby.id}`);
    } catch (error: any) {
      setError(error.message);
      setTimeout(() => setError(''), 5000);
    } finally {
      setLoading(false);
    }
  };

  const handleJoinLobby = async (lobbyId: string, password?: string) => {
    if (loading) return;

    setLoading(true);
    try {
      const player: Player = {
        id: gameAPI.getPlayerId(),
        username,
        color: PLAYER_COLORS[Math.floor(Math.random() * PLAYER_COLORS.length)],
        isHost: false,
        isConnected: true
      };

      await gameAPI.joinLobby(lobbyId, player, password);
      router.push(`/lobby/${lobbyId}`);
    } catch (error: any) {
      setError(error.message);
      setTimeout(() => setError(''), 5000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <UsernameModal
        isOpen={showUsernameModal}
        onSubmit={handleUsernameSubmit}
      />

      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">WordSpy</h1>
          <p className="text-gray-600">Find imposteren blandt jer!</p>
          {username && (
            <p className="text-sm text-gray-500 mt-2">
              Spiller som: <span className="font-semibold">{username}</span>
            </p>
          )}
        </header>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <CreateLobbyForm onCreateLobby={handleCreateLobby} />
          <LobbyList lobbies={lobbies} onJoinLobby={handleJoinLobby} />
        </div>
      </div>
    </div>
  );
}
